#ifndef DEBUG_UTILS_H
#define DEBUG_UTILS_H

#include <Arduino.h>

/* ------------------------------------------------------------------------
 * 调试级别定义
 * ------------------------------------------------------------------------ */
#define DEBUG_LEVEL_NONE    0   // 无调试输出
#define DEBUG_LEVEL_ERROR   1   // 仅错误信息
#define DEBUG_LEVEL_WARN    2   // 警告和错误信息
#define DEBUG_LEVEL_INFO    3   // 信息、警告和错误
#define DEBUG_LEVEL_DEBUG   4   // 所有调试信息
#define DEBUG_LEVEL_VERBOSE 5   // 详细调试信息

/* ------------------------------------------------------------------------
 * 调试配置 - 可以在编译时修改这些值来控制调试输出
 * ------------------------------------------------------------------------ */
#ifndef GLOBAL_DEBUG_LEVEL
#define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_ERROR    // 默认只输出错误信息
#endif

#ifndef BT_DEBUG_LEVEL
#define BT_DEBUG_LEVEL DEBUG_LEVEL_ERROR        // 蓝牙相关调试级别
#endif

#ifndef FILE_DEBUG_LEVEL
#define FILE_DEBUG_LEVEL DEBUG_LEVEL_ERROR      // 文件传输调试级别
#endif

#ifndef GIF_DEBUG_LEVEL
#define GIF_DEBUG_LEVEL DEBUG_LEVEL_ERROR       // GIF播放调试级别
#endif

/* ------------------------------------------------------------------------
 * 调试宏定义
 * ------------------------------------------------------------------------ */
#define DEBUG_PRINT_ENABLED(level, module_level) ((level) <= (module_level))

// 全局调试宏
#define DEBUG_ERROR(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_ERROR, GLOBAL_DEBUG_LEVEL)) Serial.printf("[ERROR] " fmt "\n", ##__VA_ARGS__); } while(0)
#define DEBUG_WARN(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_WARN, GLOBAL_DEBUG_LEVEL)) Serial.printf("[WARN] " fmt "\n", ##__VA_ARGS__); } while(0)
#define DEBUG_INFO(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_INFO, GLOBAL_DEBUG_LEVEL)) Serial.printf("[INFO] " fmt "\n", ##__VA_ARGS__); } while(0)
#define DEBUG_DEBUG(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_DEBUG, GLOBAL_DEBUG_LEVEL)) Serial.printf("[DEBUG] " fmt "\n", ##__VA_ARGS__); } while(0)
#define DEBUG_VERBOSE(fmt, ...) do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_VERBOSE, GLOBAL_DEBUG_LEVEL)) Serial.printf("[VERBOSE] " fmt "\n", ##__VA_ARGS__); } while(0)

// 蓝牙调试宏
#define BT_ERROR(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_ERROR, BT_DEBUG_LEVEL)) Serial.printf("[BT_ERROR] " fmt "\n", ##__VA_ARGS__); } while(0)
#define BT_WARN(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_WARN, BT_DEBUG_LEVEL)) Serial.printf("[BT_WARN] " fmt "\n", ##__VA_ARGS__); } while(0)
#define BT_INFO(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_INFO, BT_DEBUG_LEVEL)) Serial.printf("[BT_INFO] " fmt "\n", ##__VA_ARGS__); } while(0)
#define BT_DEBUG(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_DEBUG, BT_DEBUG_LEVEL)) Serial.printf("[BT_DEBUG] " fmt "\n", ##__VA_ARGS__); } while(0)
#define BT_VERBOSE(fmt, ...) do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_VERBOSE, BT_DEBUG_LEVEL)) Serial.printf("[BT_VERBOSE] " fmt "\n", ##__VA_ARGS__); } while(0)

// 文件传输调试宏
#define FILE_ERROR(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_ERROR, FILE_DEBUG_LEVEL)) Serial.printf("[FILE_ERROR] " fmt "\n", ##__VA_ARGS__); } while(0)
#define FILE_WARN(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_WARN, FILE_DEBUG_LEVEL)) Serial.printf("[FILE_WARN] " fmt "\n", ##__VA_ARGS__); } while(0)
#define FILE_INFO(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_INFO, FILE_DEBUG_LEVEL)) Serial.printf("[FILE_INFO] " fmt "\n", ##__VA_ARGS__); } while(0)
#define FILE_DEBUG(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_DEBUG, FILE_DEBUG_LEVEL)) Serial.printf("[FILE_DEBUG] " fmt "\n", ##__VA_ARGS__); } while(0)
#define FILE_VERBOSE(fmt, ...) do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_VERBOSE, FILE_DEBUG_LEVEL)) Serial.printf("[FILE_VERBOSE] " fmt "\n", ##__VA_ARGS__); } while(0)

// GIF播放调试宏
#define GIF_ERROR(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_ERROR, GIF_DEBUG_LEVEL)) Serial.printf("[GIF_ERROR] " fmt "\n", ##__VA_ARGS__); } while(0)
#define GIF_WARN(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_WARN, GIF_DEBUG_LEVEL)) Serial.printf("[GIF_WARN] " fmt "\n", ##__VA_ARGS__); } while(0)
#define GIF_INFO(fmt, ...)    do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_INFO, GIF_DEBUG_LEVEL)) Serial.printf("[GIF_INFO] " fmt "\n", ##__VA_ARGS__); } while(0)
#define GIF_DEBUG(fmt, ...)   do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_DEBUG, GIF_DEBUG_LEVEL)) Serial.printf("[GIF_DEBUG] " fmt "\n", ##__VA_ARGS__); } while(0)
#define GIF_VERBOSE(fmt, ...) do { if (DEBUG_PRINT_ENABLED(DEBUG_LEVEL_VERBOSE, GIF_DEBUG_LEVEL)) Serial.printf("[GIF_VERBOSE] " fmt "\n", ##__VA_ARGS__); } while(0)

/* ------------------------------------------------------------------------
 * 性能监控宏
 * ------------------------------------------------------------------------ */
#define PERF_MONITOR_ENABLED (GLOBAL_DEBUG_LEVEL >= DEBUG_LEVEL_DEBUG)

#if PERF_MONITOR_ENABLED
#define PERF_START(name) unsigned long perf_start_##name = micros()
#define PERF_END(name) do { \
    unsigned long perf_duration = micros() - perf_start_##name; \
    if (perf_duration > 1000) { \
        Serial.printf("[PERF] %s took %lu us\n", #name, perf_duration); \
    } \
} while(0)
#else
#define PERF_START(name)
#define PERF_END(name)
#endif

/* ------------------------------------------------------------------------
 * 运行时调试级别控制函数
 * ------------------------------------------------------------------------ */
class DebugController {
public:
    static void setGlobalDebugLevel(int level);
    static void setBTDebugLevel(int level);
    static void setFileDebugLevel(int level);
    static void setGIFDebugLevel(int level);
    
    static int getGlobalDebugLevel();
    static int getBTDebugLevel();
    static int getFileDebugLevel();
    static int getGIFDebugLevel();
    
    static void printDebugStatus();
    
private:
    static int globalLevel;
    static int btLevel;
    static int fileLevel;
    static int gifLevel;
};

/* ------------------------------------------------------------------------
 * 条件编译的快速调试开关
 * ------------------------------------------------------------------------ */
// 在需要快速禁用所有调试输出时，可以定义这个宏
#ifdef DISABLE_ALL_DEBUG
#undef DEBUG_ERROR
#undef DEBUG_WARN
#undef DEBUG_INFO
#undef DEBUG_DEBUG
#undef DEBUG_VERBOSE
#undef BT_ERROR
#undef BT_WARN
#undef BT_INFO
#undef BT_DEBUG
#undef BT_VERBOSE
#undef FILE_ERROR
#undef FILE_WARN
#undef FILE_INFO
#undef FILE_DEBUG
#undef FILE_VERBOSE
#undef GIF_ERROR
#undef GIF_WARN
#undef GIF_INFO
#undef GIF_DEBUG
#undef GIF_VERBOSE

#define DEBUG_ERROR(fmt, ...)
#define DEBUG_WARN(fmt, ...)
#define DEBUG_INFO(fmt, ...)
#define DEBUG_DEBUG(fmt, ...)
#define DEBUG_VERBOSE(fmt, ...)
#define BT_ERROR(fmt, ...)
#define BT_WARN(fmt, ...)
#define BT_INFO(fmt, ...)
#define BT_DEBUG(fmt, ...)
#define BT_VERBOSE(fmt, ...)
#define FILE_ERROR(fmt, ...)
#define FILE_WARN(fmt, ...)
#define FILE_INFO(fmt, ...)
#define FILE_DEBUG(fmt, ...)
#define FILE_VERBOSE(fmt, ...)
#define GIF_ERROR(fmt, ...)
#define GIF_WARN(fmt, ...)
#define GIF_INFO(fmt, ...)
#define GIF_DEBUG(fmt, ...)
#define GIF_VERBOSE(fmt, ...)
#endif

#endif // DEBUG_UTILS_H
