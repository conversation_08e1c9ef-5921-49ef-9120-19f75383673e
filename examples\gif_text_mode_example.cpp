/**
 * @file gif_text_mode_example.cpp
 * @brief GIF+文本混合显示模式使用示例
 * 
 * 本文件展示如何使用新的GIF+文本混合显示模式系统
 * 实现四种不同的布局模式，每种模式都支持完整的文本特效
 */

#include "gif_text_modes.h"
#include "score_mode.h"
#include "gif_player.h"

// ==================== 🔥 示例1: 模式1使用 (左GIF + 右上下文本) ====================

void example_mode1_basic() {
    printf("\n🎮 === 示例1: 模式1基础使用 ===\n");
    
    // 1. 初始化模式1
    initGifTextMode(GIF_TEXT_MODE_1);
    
    // 2. 启动GIF显示 (左侧32x32区域)
    startGifInMode("/gifs/demo.gif", GIF_TEXT_MODE_1);
    
    // 3. 创建测试文本数据 (16x16字体)
    uint16_t upper_text[16] = {
        0xFFFF, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001,
        0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0x8001, 0xFFFF
    };
    
    uint16_t lower_text[16] = {
        0x0000, 0x7FFE, 0x4002, 0x4002, 0x4002, 0x4002, 0x4002, 0x4002,
        0x4002, 0x4002, 0x4002, 0x4002, 0x4002, 0x4002, 0x7FFE, 0x0000
    };
    
    // 4. 设置右上文本区域 (索引0)
    setModeTextData(GIF_TEXT_MODE_1, 0, upper_text, 1, COLOR_RED);
    
    // 5. 设置右下文本区域 (索引1)
    setModeTextData(GIF_TEXT_MODE_1, 1, lower_text, 1, COLOR_GREEN);
    
    // 6. 绘制文本区域
    drawModeTextRegion(GIF_TEXT_MODE_1, 0);
    drawModeTextRegion(GIF_TEXT_MODE_1, 1);
    
    printf("✅ 模式1基础设置完成\n");
}

void example_mode1_with_effects() {
    printf("\n🎮 === 示例1: 模式1特效使用 ===\n");
    
    // 确保模式1已激活
    if (getCurrentGifTextMode() != GIF_TEXT_MODE_1) {
        initGifTextMode(GIF_TEXT_MODE_1);
    }
    
    // 为右上文本区域添加滚动特效
    setModeTextScroll(GIF_TEXT_MODE_1, 0, true, 1, 5); // 向左滚动，速度5
    
    // 为右下文本区域添加闪烁特效
    setModeTextBlink(GIF_TEXT_MODE_1, 1, true, 7); // 闪烁速度7
    
    printf("✅ 模式1特效设置完成\n");
}

// ==================== 🔥 示例2: 模式2使用 (左GIF + 右单文本) ====================

void example_mode2_basic() {
    printf("\n🎮 === 示例2: 模式2基础使用 ===\n");
    
    // 1. 切换到模式2
    switchGifTextMode(GIF_TEXT_MODE_2);
    
    // 2. 启动GIF显示 (左侧32x32区域)
    startGifInMode("/gifs/clock.gif", GIF_TEXT_MODE_2);
    
    // 3. 创建32x32文本数据
    uint16_t large_text[64];
    for (int i = 0; i < 64; i++) {
        large_text[i] = (i % 2 == 0) ? 0xFFFF : 0x0000; // 棋盘图案
    }
    
    // 4. 设置右侧文本区域 (索引0，32x32字体)
    setModeTextData(GIF_TEXT_MODE_2, 0, large_text, 1, COLOR_BLUE);
    
    // 5. 绘制文本区域
    drawModeTextRegion(GIF_TEXT_MODE_2, 0);
    
    printf("✅ 模式2基础设置完成\n");
}

// ==================== 🔥 示例3: 模式3使用 (左上下文本 + 右GIF) ====================

void example_mode3_basic() {
    printf("\n🎮 === 示例3: 模式3基础使用 ===\n");
    
    // 1. 切换到模式3
    switchGifTextMode(GIF_TEXT_MODE_3);
    
    // 2. 启动GIF显示 (右侧32x32区域)
    startGifInMode("/gifs/animation.gif", GIF_TEXT_MODE_3);
    
    // 3. 创建测试文本数据
    uint16_t text_data1[16] = {
        0xF0F0, 0xF0F0, 0xF0F0, 0xF0F0, 0xF0F0, 0xF0F0, 0xF0F0, 0xF0F0,
        0x0F0F, 0x0F0F, 0x0F0F, 0x0F0F, 0x0F0F, 0x0F0F, 0x0F0F, 0x0F0F
    };
    
    uint16_t text_data2[16] = {
        0xAAAA, 0x5555, 0xAAAA, 0x5555, 0xAAAA, 0x5555, 0xAAAA, 0x5555,
        0x5555, 0xAAAA, 0x5555, 0xAAAA, 0x5555, 0xAAAA, 0x5555, 0xAAAA
    };
    
    // 4. 设置左上文本区域 (索引0)
    setModeTextData(GIF_TEXT_MODE_3, 0, text_data1, 1, COLOR_YELLOW);
    
    // 5. 设置左下文本区域 (索引1)
    setModeTextData(GIF_TEXT_MODE_3, 1, text_data2, 1, COLOR_CYAN);
    
    // 6. 绘制文本区域
    drawModeTextRegion(GIF_TEXT_MODE_3, 0);
    drawModeTextRegion(GIF_TEXT_MODE_3, 1);
    
    printf("✅ 模式3基础设置完成\n");
}

// ==================== 🔥 示例4: 模式4使用 (左单文本 + 右GIF) ====================

void example_mode4_basic() {
    printf("\n🎮 === 示例4: 模式4基础使用 ===\n");
    
    // 1. 切换到模式4
    switchGifTextMode(GIF_TEXT_MODE_4);
    
    // 2. 启动GIF显示 (右侧32x32区域)
    startGifInMode("/gifs/logo.gif", GIF_TEXT_MODE_4);
    
    // 3. 创建32x32文本数据 (圆形图案)
    uint16_t circle_text[64];
    for (int i = 0; i < 64; i++) {
        // 简单的圆形图案生成
        int row = i / 2;
        int bit = i % 2;
        if (row >= 8 && row <= 23) {
            circle_text[i] = 0x00FF; // 中间部分
        } else {
            circle_text[i] = 0x0000; // 边缘部分
        }
    }
    
    // 4. 设置左侧文本区域 (索引0，32x32字体)
    setModeTextData(GIF_TEXT_MODE_4, 0, circle_text, 1, COLOR_MAGENTA);
    
    // 5. 绘制文本区域
    drawModeTextRegion(GIF_TEXT_MODE_4, 0);
    
    printf("✅ 模式4基础设置完成\n");
}

// ==================== 🔥 示例5: 动态模式切换演示 ====================

void example_dynamic_mode_switching() {
    printf("\n🎮 === 示例5: 动态模式切换演示 ===\n");
    
    const char* gif_files[] = {
        "/gifs/mode1.gif",
        "/gifs/mode2.gif", 
        "/gifs/mode3.gif",
        "/gifs/mode4.gif"
    };
    
    for (int mode = 1; mode <= 4; mode++) {
        printf("🔄 切换到模式 %d\n", mode);
        
        // 切换模式
        switchGifTextMode((GifTextMode)mode);
        
        // 启动对应的GIF
        startGifInMode(gif_files[mode-1], (GifTextMode)mode);
        
        // 根据模式设置文本
        int textRegionCount = getModeTextRegionCount((GifTextMode)mode);
        printf("📝 模式 %d 有 %d 个文本区域\n", mode, textRegionCount);
        
        // 为每个文本区域设置测试数据
        for (int i = 0; i < textRegionCount; i++) {
            if (mode == 2 || mode == 4) {
                // 32x32字体模式
                uint16_t test_data[64];
                for (int j = 0; j < 64; j++) {
                    test_data[j] = (j + mode * 16) % 2 ? 0xFFFF : 0x0000;
                }
                setModeTextData((GifTextMode)mode, i, test_data, 1, COLOR_WHITE);
            } else {
                // 16x16字体模式
                uint16_t test_data[16];
                for (int j = 0; j < 16; j++) {
                    test_data[j] = (j + mode * 4 + i * 8) % 2 ? 0xFFFF : 0x0000;
                }
                setModeTextData((GifTextMode)mode, i, test_data, 1, COLOR_WHITE);
            }
            
            drawModeTextRegion((GifTextMode)mode, i);
        }
        
        // 模拟显示时间
        delay(3000);
    }
    
    printf("✅ 动态模式切换演示完成\n");
}

// ==================== 🔥 示例6: 完整特效演示 ====================

void example_full_effects_demo() {
    printf("\n🎮 === 示例6: 完整特效演示 ===\n");
    
    // 使用模式1进行特效演示
    initGifTextMode(GIF_TEXT_MODE_1);
    startGifInMode("/gifs/effects_demo.gif", GIF_TEXT_MODE_1);
    
    // 创建多字符文本数据用于滚动演示
    uint16_t scroll_text[64]; // 4个字符的16x16数据
    for (int i = 0; i < 64; i++) {
        scroll_text[i] = (i / 16 + 1) * 0x1111; // 不同字符不同亮度
    }
    
    // 设置上半屏文本并启用滚动
    setModeTextData(GIF_TEXT_MODE_1, 0, scroll_text, 4, COLOR_GREEN);
    setModeTextScroll(GIF_TEXT_MODE_1, 0, true, 1, 3); // 左滚动，速度3
    
    // 设置下半屏文本并启用闪烁
    setModeTextData(GIF_TEXT_MODE_1, 1, scroll_text + 32, 2, COLOR_RED);
    setModeTextBlink(GIF_TEXT_MODE_1, 1, true, 5); // 闪烁速度5
    
    printf("🌊 上半屏: 滚动特效演示\n");
    printf("💫 下半屏: 闪烁特效演示\n");
    printf("🎬 GIF: 左侧区域播放\n");
    
    printf("✅ 完整特效演示设置完成\n");
}

// ==================== 🔥 主演示函数 ====================

void runGifTextModeExamples() {
    printf("\n🚀 ===== GIF+文本混合模式演示开始 =====\n");
    
    // 运行所有示例
    example_mode1_basic();
    delay(2000);
    
    example_mode1_with_effects();
    delay(3000);
    
    example_mode2_basic();
    delay(2000);
    
    example_mode3_basic();
    delay(2000);
    
    example_mode4_basic();
    delay(2000);
    
    example_dynamic_mode_switching();
    delay(1000);
    
    example_full_effects_demo();
    delay(5000);
    
    // 清理
    clearGifTextMode();
    
    printf("🎉 ===== GIF+文本混合模式演示完成 =====\n");
}
