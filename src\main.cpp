#include "BluetoothSerial.h"
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "bluetooth_protocol.h"
#include "LittleFS.h"
#include "AnimatedGIF.h"
#include "config.h"
#include "file_handler.h"
#include "gif_player.h"
#include "debug_utils.h"
#include "clock_mode.h"
#include "score_mode.h"
#include "DisplayDriver.h"
#include "LEDController.h"
#include "FontData.h"
#include "app_list.h"

void testCompleteBluetoothToDisplayProcess();
 void testGifTextModeEffects();                         

String device_name = BT_DEVICE_NAME;
BluetoothProtocolParser btParser; // 蓝牙协议解析器
BluetoothFrame currentFrame;      // 当前解析的帧
loopstate currentloopstate = loopstate::loop_state_gif; // 初始化循环状态为GIF播放

// Check if Bluetooth is available
#if !defined(CONFIG_BT_ENABLED) || !defined(CONFIG_BLUEDROID_ENABLED)
#error Bluetooth is not enabled! Please run `make menuconfig` to and enable it
#endif

// Check Serial Port Profile
#if !defined(CONFIG_BT_SPP_ENABLED)
#error Serial Port Profile for Bluetooth is not available or not enabled. It is only available for the ESP32 chip.
#endif

BluetoothSerial SerialBT;

// 函数声明
void handleParseResult(ParseResult result);
void processBluetoothCommand(const BluetoothFrame &frame);
void handleTextCommand(const BluetoothFrame &frame);
void handleUpperTextCommand(const uint16_t *fontData, int charCount); // 独立处理上半屏
void handleLowerTextCommand(const uint16_t *fontData, int charCount); // 独立处理下半屏
void handleColorCommand(const BluetoothFrame &frame);
void handleBorderCommand(const BluetoothFrame &frame);
void handleSpecificColorCommand(const BluetoothFrame &frame);
void handleRandomColorCommand(const BluetoothFrame &frame);     //列表函数
void handleAppListStartCommand(const BluetoothFrame &frame);
void handleAppListEndCommand(const BluetoothFrame &frame);
void handleAppListDataCommand(const BluetoothFrame &frame);
void handleFileTransferMode();
void handleNormalMode();
void handleClockModeCommand(const BluetoothFrame &frame);  // 处理时钟模式命令
void handleScoreModeCommand(const BluetoothFrame &frame);  // 处理计分模式命令
// ==================== 全局清理函数声明 ====================
void clearEntireScreen();                                 // 全局清屏函数
void exitAllModes();                                      // 统一退出所有模式
void safeModeSwitchToClockMode();                        // 安全切换到时钟模式
void safeModeSwitchToScoreMode();                        // 安全切换到计分模式
// ==================== 测试函数声明 ====================
void xtestRegionalUpdateOptimization();
// 全局测试状态变量
static unsigned long testStartTime = 0;
static unsigned long lastCommandTime = 0;
static uint8_t testPhase = 0;  // 0=计分模式测试, 1=时钟模式测试
static uint16_t leftScore = 999;   // 左下区域从999开始
static uint16_t rightScore = 1;    // 右下区域从1开始
static uint8_t commandCount = 0;
static bool testInitialized = false;
static bool textDisplayed = false;  // 文字区域是否已显示

// 简化的文字数据（24字节，1个字符用于测试）
static uint8_t sampleTextData[24] = {
    0x10, 0x00, 0x10, 0x00, 0xFF, 0x01, 0x11, 0x01,
    0x11, 0x01, 0xFF, 0x01, 0x11, 0x01, 0x11, 0x01,
    0xFF, 0x01, 0x11, 0x04, 0x10, 0x04, 0xE0, 0x07
};



void setup()
{
    // 1. 初始化串口和蓝牙
    Serial.begin(SERIAL_BAUD_RATE);
    SerialBT.begin(device_name); 

    // 2. 初始化LittleFS文件系统
    if (!initLittleFS()) {
        DEBUG_ERROR("LittleFS initialization failed!");
        return;
    }

    // 创建gifs目录
    createGifsDirectory();

    // 显示内存信息
    printMemoryInfo();

    // 初始化LED矩阵屏
    if (!initLEDMatrix()) {
        DEBUG_ERROR("LED matrix initialization failed!");
        return;
    }

   // 5. 播放启动GIF动画
    if (!playGIFAuto(GIF_STARTUP_FILE)) {
        DEBUG_WARN("Unable to play startup animation");
        // 显示内存信息以帮助调试
        // printMemoryInfo();
    }

    // 6. 显示调试级别信息
    DebugController::printDebugStatus();

    // 7. 显示系统信息
    DEBUG_INFO("=== ESP32 Bluetooth LED Controller ===");
    DEBUG_INFO("Device name: %s", device_name.c_str());
    DEBUG_INFO("Bluetooth protocol parser initialized");
    DEBUG_INFO("Waiting for Bluetooth connection and data...");

    // 性能提示
    if (GLOBAL_DEBUG_LEVEL > DEBUG_LEVEL_ERROR ||
        BT_DEBUG_LEVEL > DEBUG_LEVEL_ERROR ||
        FILE_DEBUG_LEVEL > DEBUG_LEVEL_ERROR) {
        Serial.println("WARNING: High debug levels may affect Bluetooth performance!");
        Serial.println("If you experience rxfull errors, consider lowering debug levels in config.h");
    }

    initSpecificColorSystem();  
}

void loop()
{
   //xtestRegionalUpdateOptimization();  //计分模式测试函数
    //testWithKnownFontData();
   //testCompleteBluetoothToDisplayProcess();  //文本模式上下屏
    //testGifTextModeEffects();        // 🎮 启动GIF+文本混合模式特效测试

   handleNormalMode();//接收蓝牙命令
    
    switch (currentloopstate) {
    case loopstate::loop_state_gif:
        // GIF播放模式
       updateGIF();
        break;
    case loopstate::loop_state_transfer:
        //接收GIF文件
        break;
    
    case loopstate::loop_state_clock:
        // 时钟模式
        if (isClockModeActive()) {
        if (current_display_mode == DISPLAY_MODE_BIG) {
         updateBigScreenDisplay();    // 大屏模式只调用大屏更新
        } else {
          updateTimeLogic();           // 小屏模式只调用小屏更新
     }
     }
        break;

    case loopstate::loop_state_score:
        // 计分模式
        if (isScoreModeActive()) {
        // 调用计分模式的更新函数
        updateScoreDisplay();           // 更新计分显示（冒号闪烁等）
        printf("🔄 Calling updateHighFrequencyScrolling\n");
        updateHighFrequencyScrolling(); // 调用点2：正式的更新逻辑
        break;

    case loopstate::loop_state_text:

        // 文本模式
        updateAllEffects();   // 更新所有特效
        updateBrightness();   // 更新亮度设置
        updateColors();       // 更新颜色状态

        // 纯文本显示模式：直接使用updateTextDisplay()
        updateTextDisplay();  // 纯文本显示更新
        break;

    case loopstate::loop_state_background:
        // 背景模式
        // 目前没有具体实现，保留以备将来扩展
        break;  

    case loopstate::loop_state_list:
        // 列表模式
        // 目前没有具体实现，保留以备将来扩展
        break;
    
    default:
        break;
        }
    }
  
    
}

// 处理解析结果
void handleParseResult(ParseResult result)
{
    switch (result)
    {
    case ParseResult::FRAME_COMPLETE:
        // 只在调试级别时输出完整帧信息，这是性能瓶颈的主要原因
        BT_DEBUG("Received complete frame - Command: 0x%02X, Data length: %d",
                 currentFrame.command, currentFrame.dataLength);
        processBluetoothCommand(currentFrame);
        btParser.reset(); // 重置解析器准备下一帧
        break;

    case ParseResult::FRAME_ERROR:
        BT_ERROR("Frame format error");

        // 丢弃接收缓冲区中的脏数据
        while(SerialBT.available()) {
            uint8_t dummy[1024];  // 临时缓冲区
            int bytesToRead = min(SerialBT.available(), 1024);
            SerialBT.readBytes(dummy, bytesToRead);  // 批量读取并丢弃
        }

        SerialBT.flush();
        btParser.Error_reset();
        SerialBT.write(BT_ERROR_FRAME_FORMAT);
        break;

    case ParseResult::INVALID_COMMAND:
        BT_ERROR("Invalid command");
        break;

    case ParseResult::DATA_TOO_LONG:
        BT_ERROR("Data too long");
        break;

    case ParseResult::NEED_MORE_DATA:
        // 继续等待更多数据，无需处理
        break;

    default:
        BT_ERROR("Unknown parse result: %d", (int)result);
        break;
    }
}



// 处理蓝牙命令
void processBluetoothCommand(const BluetoothFrame &frame)
{
    if (!frame.isValidCommand())
    {
        Serial.println("Error: Invalid command frame");
        return;
    }

   // exitAllModes(); // 确保退出所有模式，避免状态冲突

    switch (frame.command)
    {
    case BT_CMD_SET_DIRECTION: // 0x00
        currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleDirectionCommand(BT_DIRECTION_HORIZONTAL);
        Serial.println("设置文本显示方向: 正向显示");
        break;

    case BT_CMD_SET_VERTICAL: // 0x01
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleDirectionCommand(BT_DIRECTION_VERTICAL);
        Serial.println("设置文本显示方向: 竖向显示");
        break;

    case BT_CMD_SET_FONT_16x16: // 0x02
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        Serial.println("设置字体: 16x16");
        resetAllStatesOnFontSwitch(); // 重置所有继承状态
        currentFontSize = BT_FONT_16x16;
        handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
        break;

    case BT_CMD_SET_FONT_32x32: // 0x03
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        Serial.println("设置字体: 32x32");
        resetAllStatesOnFontSwitch(); // 重置所有继承状态
        currentFontSize = BT_FONT_32x32;
        handleFullScreenTextCommand(full_text, getFullTextCharCount());
        break;

    case BT_CMD_SET_TEXT: // 0x04
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleTextCommand(frame);
        break;

    case BT_CMD_SET_COLOR: // 0x06
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleColorCommand(frame);
        break;

    case BT_CMD_SET_BRIGHTNESS: // 0x07
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleBrightnessCommand(frame);
        break;

    case BT_CMD_SET_EFFECT: // 0x08
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleEffectCommand(frame);
        break;

    case BT_CMD_SET_BORDER: // 0x09
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleBorderCommand(frame);
        break;

    case BT_CMD_SET_SPECIFIC_COLOR: // 0x0A
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleSpecificColorCommand(frame);
        break;

    case BT_CMD_SET_RANDOM_COLOR: // 0x0B
    currentloopstate = loopstate::loop_state_text; // 切换到文本模式
        handleRandomColorCommand(frame);
        break;

    case BT_CMD_FILE_START://0x10
    currentloopstate = loopstate::loop_state_transfer; // 切换到文件传输模式
        handleFileStartCommand(frame);
        break;

    case BT_CMD_FILE_DATA://0x11
        handleFileDataCommand(frame);
        break;

    case BT_CMD_FILE_END://0x12
    currentloopstate = loopstate::loop_state_transfer; // 切换到文件传输模式
        handleFileEndCommand(frame);
        break;

    case BT_CMD_FILE_LIST://0x13
        handleFileListCommand(frame);
        break;

    case BT_CMD_FILE_DELETE://0x14
        handleFileDeleteCommand(frame);
        break;

    case BT_CMD_PLAY_GIF://0x15
        currentloopstate = loopstate::loop_state_gif; // 切换到GIF播放模式
        handlePlayGifCommand(frame);
        break;

    case BT_CMD_CLOCK_MODE: // 0x20
        currentloopstate = loopstate::loop_state_clock; // 切换到时钟模式
        clearEntireScreen();
        handleClockModeCommand(frame);
        break;

    case BT_CMD_SCORE_MODE: // 0x21
        currentloopstate = loopstate::loop_state_score; // 切换到计分模式
        // 🔥 智能清屏：只在非计分模式时清屏，避免连续命令重复刷新
        if (!isScoreModeActive()) {
            Serial.println("🧹 首次进入计分模式：执行全局清屏");
            clearEntireScreen();
        } else {
            Serial.println("📊 已在计分模式：跳过全局清屏，进行区域更新");
        }
        handleScoreModeCommand(frame);
        break;

    case BT_CMD_LIST_START://0x30
        Serial.println("处理列表开始命令");
        handleAppListStartCommand(frame);
        break;

    case BT_CMD_LIST_DATA://0x31
        Serial.println("处理列表数据命令");
        handleAppListDataCommand(frame);
        break;

    case BT_CMD_LIST_END://0x32
        currentloopstate = loopstate::loop_state_list; // 切换到列表模式
        handleAppListEndCommand(frame);
        break;

    default:
        BT_WARN("Unsupported command: 0x%02X", frame.command);
        break;
    }
}

// 处理文本命令 (0x04)
void handleTextCommand(const BluetoothFrame &frame)
{
    // 增加内存安全检查和调试信息
    Serial.printf("收到文本命令 - 帧有效性: %s, 数据长度: %d, 拥有数据: %s\n",
                  frame.isValid ? "是" : "否", frame.dataLength, frame.ownsData ? "是" : "否");

    if (!frame.hasValidData())
    {
        Serial.println("错误: 帧数据无效，跳过处理");
        return;
    }

    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData32x32(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理32x32文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);
            handleFullScreenTextCommand(fontData, charCount);
        }
        else
        {
            Serial.printf("错误: 32x32字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
    else
    {
        // 16x16字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData16x16(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理16x16文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);

            switch (screenArea)
            {
            case BT_SCREEN_UPPER: // 上半屏
                handleUpperTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_LOWER: // 下半屏
                handleLowerTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_BOTH: // 全屏 (分为上下两部分)
            {
                int halfCount = charCount / 2;
                const uint16_t *upperData = fontData;
                const uint16_t *lowerData = fontData + (halfCount * 16); // 每个字符16个uint16_t
                Serial.printf("全屏模式拆分 - 上半屏: %d字符, 下半屏: %d字符\n", halfCount, charCount - halfCount);
                handleTextCommand(upperData, halfCount, lowerData, charCount - halfCount);
            }
            break;
            default:
                Serial.printf("错误: 无效的屏幕区域 0x%02X\n", screenArea);
                break;
            }
        }
        else
        {
            Serial.printf("错误: 16x16字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
}

// 独立处理上半屏文本（保持下半屏不变）
void handleUpperTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置上半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新上半屏文本时清除上半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到上半屏文本变更，清除上半屏特定字符颜色");
        clearUpperSpecificColors();
    }

    // 只释放上半屏数据
    if (dynamic_upper_text)
    {
        free(dynamic_upper_text);
        dynamic_upper_text = nullptr;
    }

    // 分配并复制上半屏数据
    int upperDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_upper_text = (uint16_t *)malloc(upperDataSize);
    if (dynamic_upper_text)
    {
        memcpy(dynamic_upper_text, fontData, upperDataSize);
        dynamic_upper_char_count = charCount;
        Serial.printf("上半屏数据已更新: %d字符, %d字节\n", charCount, upperDataSize);
    }
    else
    {
        Serial.println("错误: 上半屏数据内存分配失败");
        dynamic_upper_char_count = 0;
    }

    // 更新显示状态（只重置上半屏索引）
    textState.upperIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 独立处理下半屏文本（保持上半屏不变）
void handleLowerTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置下半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新下半屏文本时清除下半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到下半屏文本变更，清除下半屏特定字符颜色");
        clearLowerSpecificColors();
    }

    // 只释放下半屏数据
    if (dynamic_lower_text)
    {
        free(dynamic_lower_text);
        dynamic_lower_text = nullptr;
    }

    // 分配并复制下半屏数据
    int lowerDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_lower_text = (uint16_t *)malloc(lowerDataSize);
    if (dynamic_lower_text)
    {
        memcpy(dynamic_lower_text, fontData, lowerDataSize);
        dynamic_lower_char_count = charCount;
        Serial.printf("下半屏数据已更新: %d字符, %d字节\n", charCount, lowerDataSize);
    }
    else
    {
        Serial.println("错误: 下半屏数据内存分配失败");
        dynamic_lower_char_count = 0;
    }

    // 更新显示状态（只重置下半屏索引）
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 处理边框命令 (0x09)
void handleBorderCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_BORDER_DATA_LEN)
    {
        Serial.printf("错误: 边框命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_BORDER_DATA_LEN);
        return;
    }

    uint8_t style, colorIndex, effect, speed;
    frame.getBorderData(style, colorIndex, effect, speed);

    Serial.printf("🎨 收到边框命令 - 样式:%d, 颜色索引:%d, 效果:%d, 速度:%d\n",
                  style, colorIndex, effect, speed);

    // 验证参数有效性
    if (style > BORDER_STYLE_RAINBOW)
    {
        Serial.printf("错误: 无效的边框样式 %d (有效范围: 0-4)\n", style);
        return;
    }

    if (colorIndex > 6)
    {
        Serial.printf("错误: 无效的颜色索引 %d (有效范围: 0-6)\n", colorIndex);
        return;
    }

    if (effect > 3)
    {
        Serial.printf("错误: 无效的边框效果 %d (有效范围: 0-3)\n", effect);
        return;
    }

    if (speed < 1 || speed > 10)
    {
        Serial.printf("错误: 无效的边框速度 %d (有效范围: 1-10)\n", speed);
        return;
    }

    // 根据边框样式进行逻辑处理
    if (style == BORDER_STYLE_NONE)
    {
        // 无边框：清除边框效果
        clearBorderEffect();
        Serial.println("✅ 边框已清除");
        return;
    }

    // 确定颜色
    uint16_t borderColor = COLOR_WHITE; // 默认颜色
    if (style == BORDER_STYLE_RAINBOW)
    {
        // 彩虹边框：颜色参数无效，使用默认白色（实际会被彩虹色覆盖）
        borderColor = COLOR_WHITE;
        Serial.println("📝 彩虹边框模式：忽略颜色选择参数");
    }
    else
    {
        // 其他边框：使用颜色索引映射
        borderColor = BORDER_COLORS[colorIndex];
        Serial.printf("📝 使用颜色: 0x%04X\n", borderColor);
    }

    // 角落边框的效果限制
    if (style == BORDER_STYLE_CORNER && (effect == 1 || effect == 2))
    {
        // 角落边框不支持流动效果，转为静止显示
        effect = 0;
        Serial.println("📝 角落边框不支持流动效果，已转为静止显示");
    }

    // 应用边框设置
    setBorderEffect(style, borderColor, effect, speed);

    // 输出最终设置信息
    const char *styleNames[] = {"无边框", "实线", "点线", "角落", "彩虹"};
    const char *colorNames[] = {"红", "绿", "蓝", "黄", "紫", "青", "白"};
    const char *effectNames[] = {"静止", "顺时针", "逆时针", "闪烁"};

    Serial.printf("✅ 边框设置完成 - %s边框, %s色, %s效果, 速度%d\n",
                  styleNames[style],
                  (style == BORDER_STYLE_RAINBOW) ? "彩虹" : colorNames[colorIndex],
                  effectNames[effect], speed);
}

// 处理特定字符颜色命令 (0x0A)
void handleSpecificColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_SPECIFIC_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 特定字符颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_SPECIFIC_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t charIndex = frame.data[1];
    uint8_t r = frame.data[2];
    uint8_t g = frame.data[3];
    uint8_t b = frame.data[4];

    Serial.printf("🎨 收到特定字符颜色命令 - 区域:%d, 索引:%d, RGB:(%d,%d,%d)\n",
                  screenArea, charIndex, r, g, b);

    // 验证参数有效性
    if (screenArea != BT_SCREEN_UPPER && screenArea != BT_SCREEN_LOWER)
    {
        Serial.printf("错误: 不支持的屏幕区域 %d (仅支持上半屏=1, 下半屏=2)\n", screenArea);
        return;
    }

    // 转换RGB888到RGB565
    uint16_t color = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);

    // 设置特定字符颜色
    setSpecificCharColor(screenArea, charIndex, color);

    Serial.printf("✅ 特定字符颜色设置完成 - 区域:%s, 索引:%d, 颜色:0x%04X\n",
                  (screenArea == BT_SCREEN_UPPER) ? "上半屏" : "下半屏",
                  charIndex, color);
}

// 处理随机颜色命令 (0x0B)
void handleRandomColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_RANDOM_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 随机颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_RANDOM_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t mode = frame.data[1];
    uint8_t interval = frame.data[2];
    uint8_t seed = frame.data[3];

    Serial.printf("🎲 收到随机颜色命令 - 区域:%d, 模式:%d, 间隔:%d, 种子:%d\n",
                  screenArea, mode, interval, seed);

    // 验证参数有效性 - 随机颜色仅支持全屏设置
    if (screenArea != BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 随机颜色仅支持全屏设置，收到区域参数: %d (应为3)\n", screenArea);
        return;
    }

    if (mode > RANDOM_COLOR_BRIGHT)
    {
        Serial.printf("错误: 无效的随机颜色模式 %d (有效范围: 0-6)\n", mode);
        return;
    }

    if (interval < 1 || interval > 10)
    {
        Serial.printf("错误: 无效的更新间隔 %d (有效范围: 1-10)\n", interval);
        return;
    }

    // 设置随机颜色模式
    setRandomColorMode(screenArea, mode, interval, seed);

    const char *areaNames[] = {"", "上半屏", "下半屏", "全屏"};
    const char *modeNames[] = {"关闭", "每字符不同", "全体相同", "彩虹色", "暖色系", "冷色系", "高亮度"};

    Serial.printf("✅ 随机颜色设置完成 - %s, %s模式, 间隔%d秒, 种子%d\n",
                  areaNames[screenArea], modeNames[mode], interval, seed);
}


// 文件传输专用模式：全力处理蓝牙数据
void handleFileTransferMode()
{
    while(SerialBT.available() > 0) {
        int byte = SerialBT.read();
        ParseResult result = btParser.parseByte(byte, currentFrame);
        handleParseResult(result);}

            // // 大幅减少yield频率，提高处理效率
            // if (processedCount % 2048 == 0) {
            //     yield();
            // }
      

    // 文件传输期间只检查传输超时，不做其他处理
    if (millis() - fileTransferTime_Every > FILE_TRANSFER_TIMEOUT) {
        FILE_WARN("File transfer timeout, aborting transfer");
        abortFileTransfer();
    }
}

// 正常模式：处理蓝牙数据和其他任务
void handleNormalMode()
{
    // 处理蓝牙接收的数据并解析协议
    int availableBytes = SerialBT.available();
    if (availableBytes > 0) {
        int processedCount = 0;
         Serial.printf("📡 检测到蓝牙数据，可用字节数: %d\n", SerialBT.available());
        // 正常模式：适中的处理容量
        while (SerialBT.available() > 0 && processedCount < 1024) {
            int byte = SerialBT.read();
            ParseResult result = btParser.parseByte(byte, currentFrame);
            handleParseResult(result);
            processedCount++;

            // 正常模式保持系统响应性
            if (processedCount % 256 == 0) {
                yield();
            }
        }
    }

    // 只在GIF模式下更新GIF动画，避免文本模式下GIF干扰
    if (currentloopstate == loopstate::loop_state_gif) {
        updateGIF();
    }

    // 检查解析器超时
    if (btParser.isFrameTimeout()) {
        BT_WARN("Frame receive timeout, resetting parser");
        btParser.reset();
    }
}

// LED矩阵屏和GIF相关函数已移动到gif_player.cpp

// 文件传输相关函数已移动到file_handler.cpp


// 处理时钟模式命令 (0x20)
void handleClockModeCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_CLOCK_DATA_LEN)
    {
        Serial.printf("错误: 时钟模式命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_CLOCK_DATA_LEN);
        return;
    }

    // 🔥 新增：在处理命令前先安全切换到时钟模式，避免残留像素
    safeModeSwitchToClockMode();

    uint8_t screenMode, gifSelect, weekdayR, weekdayG, weekdayB;
    uint8_t timeR, timeG, timeB, hour, minute, weekday, language;

    frame.getClockModeData(screenMode, gifSelect, weekdayR, weekdayG, weekdayB,
                          timeR, timeG, timeB, hour, minute, weekday, language);

    Serial.printf("🕐 收到时钟模式命令\n");
    Serial.printf("  屏幕模式: %s\n", (screenMode == 0) ? "小屏" : "大屏");
    Serial.printf("  GIF选择: %d (预留)\n", gifSelect);
    Serial.printf("  星期颜色: RGB(%d,%d,%d)\n", weekdayR, weekdayG, weekdayB);
    Serial.printf("  时间颜色: RGB(%d,%d,%d)\n", timeR, timeG, timeB);
    Serial.printf("  时间: %02d:%02d\n", hour, minute);
    Serial.printf("  星期: %d\n", weekday);
    Serial.printf("  语言: %s\n", (language == 0) ? "中文" : "英文");

    // 调用时钟模式处理函数
    handleBluetoothClockMode(screenMode, gifSelect, weekdayR, weekdayG, weekdayB,
                            timeR, timeG, timeB, hour, minute, weekday, language);

    Serial.println("✅ 时钟模式命令处理完成");
}

// 处理计分模式命令 (0x21)
void handleScoreModeCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData())
    {
        Serial.printf("错误: 计分模式命令数据无效\n");
        return;
    }

    // 🔥 智能模式切换：只在必要时进行模式切换，避免重复清屏
    if (!isScoreModeActive()) {
        Serial.println("🔄 切换到计分模式");
        safeModeSwitchToScoreMode();
    } else {
        Serial.println("📊 已在计分模式，直接处理命令");
    }

    // 根据数据长度判断是文本命令还是数字命令
    if (frame.dataLength >= BT_SCORE_TEXT_DATA_LEN_MIN && frame.dataLength <= BT_SCORE_TEXT_DATA_LEN_MAX)
    {
        // 文本命令处理 - 支持动态长度（最多10个字符，240字节）
        uint8_t region;
        uint8_t textData[BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES];  // 最大240字节缓冲区
        uint16_t textDataLength;
        frame.getScoreModeTextData(region, textData, textDataLength);

        Serial.printf("📝 收到计分模式文本命令\n");
        Serial.printf("  区域: %d (%s)\n", region,
                     (region == 0) ? "左上" : (region == 2) ? "右上" : "未知");
        Serial.printf("  文本数据长度: %d 字节\n", textDataLength);
        Serial.printf("  字符数量: %d 个\n", textDataLength / BT_SCORE_CHAR_BYTES);

        // 调用计分模式文本处理函数
        handleBluetoothScoreModeText(region, textData, textDataLength);

        Serial.println("✅ 计分模式文本命令处理完成");
    }
    else if (frame.dataLength == BT_SCORE_NUM_DATA_LEN)
    {
        // 数字命令处理
        uint8_t region, colorR, colorG, colorB, operation;
        uint16_t initValue, operValue;
        frame.getScoreModeNumData(region, colorR, colorG, colorB, initValue, operation, operValue);

        Serial.printf("🔢 收到计分模式数字命令\n");
        Serial.printf("  区域: %d (%s)\n", region,
                     (region == 1) ? "左下" : (region == 3) ? "右下" : "未知");
        Serial.printf("  颜色: RGB(%d,%d,%d)\n", colorR, colorG, colorB);
        Serial.printf("  初始值: %d\n", initValue);
        Serial.printf("  操作: %s %d\n", (operation == 0) ? "加" : "减", operValue);

        // 调用计分模式数字处理函数
        handleBluetoothScoreModeNumber(region, colorR, colorG, colorB, initValue, operation, operValue);

        Serial.println("✅ 计分模式数字命令处理完成");
    }
    else
    {
        Serial.printf("错误: 计分模式命令数据长度无效 - 数据长度: %d, 期望: %d~%d(文本) 或 %d(数字)\n",
                      frame.dataLength, BT_SCORE_TEXT_DATA_LEN_MIN, BT_SCORE_TEXT_DATA_LEN_MAX, BT_SCORE_NUM_DATA_LEN);
    }
}


void safeModeSwitchToClockMode() {
    printf("🔄 安全切换到时钟模式...\n");

    // 1. 退出所有其他模式
    exitAllModes();

    // 2. 短暂延时确保清理完成
    delay(50);

    // 3. 进入时钟模式
    enterClockMode();

    printf("✅ 已安全切换到时钟模式\n");
}

/**
 * 安全切换到计分模式
 * 先清理所有模式，再进入计分模式，避免残留像素
 */
void safeModeSwitchToScoreMode() {
    printf("🔄 安全切换到计分模式...\n");

    // 1. 退出所有其他模式
    exitAllModes();

    // 2. 短暂延时确保清理完成
    delay(50);

    // 3. 初始化并进入计分模式
    if (initScoreMode()) {
        enterScoreMode();
        printf("✅ 已安全切换到计分模式\n");
    } else {
        printf("❌ 错误: 计分模式初始化失败\n");
    }
}


// ==================== 全局清理函数实现 ====================

/**
 * 全局清屏函数 - 清除整个显示屏的所有像素
 * 用于解决模式切换时的残留像素问题
 */
void clearEntireScreen() {
    extern MatrixPanel_I2S_DMA* dma_display;
    if (dma_display) {
        printf("🧹 执行全局清屏...\n");
        dma_display->fillScreen(COLOR_BLACK);
        // 也可以使用 dma_display->clearScreen();
        printf("✅ 全局清屏完成\n");
    } else {
        printf("❌ 错误: 显示驱动未初始化\n");
    }
}

/**
 * 统一退出所有模式函数
 * 确保完全清理所有模式的显示和状态
 */
void exitAllModes() {
    printf("🔄 开始退出所有模式...\n");

    // 检查并退出时钟模式
    if (isClockModeActive()) {
        printf("  📅 退出时钟模式\n");
        exitClockMode();
    }

    // 检查并退出计分模式
    if (isScoreModeActive()) {
        printf("  🏆 退出计分模式\n");
        exitScoreMode();
    }

    // 清理文本显示状态（纯文本模式不需要分区数据清理）
    printf("  📝 清理文本显示状态\n");

    // 全局清屏确保没有残留
    clearEntireScreen();

    printf("✅ 所有模式已安全退出\n");
}




void xtestRegionalUpdateOptimization() {
    unsigned long currentTime = millis();
    
 // 初始化测试
    if (!testInitialized) {
        Serial.println("\n🧪 ===== 区域选择性更新优化测试（修复版） =====");
        Serial.println("📋 验证：连续计分命令只更新指定区域，其他区域保持不变");
        Serial.println("🎯 测试区域：左上文字 + 右上文字 + 左下数字 + 右下数字 + 中间冒号");
        testStartTime = currentTime;
        testInitialized = true;
        testPhase = 0;
        commandCount = 0;
        textDisplayed = false;
        lastCommandTime = currentTime;
        Serial.println("🔢 开始计分模式测试阶段 (5秒)");
        return;
    }
    
    // 计分模式测试阶段 (5秒)
    if (testPhase == 0) {
        // 首先显示文字区域（只在开始时显示一次）
        if (!textDisplayed && (currentTime - testStartTime > 500)) {
            Serial.println("📝 显示左上区域文字（1个字符测试）");
            
            // 发送左上文字区域命令 - 修正的数据包格式
            uint8_t leftTextPacket[] = {
                0xAA, 0x55,                    // 包头
                0x21,                          // 计分模式命令
                0x01, 0x41,                    // 数据长度 
                0x00,                          // 左上区域 (0x00=左上文字区域)
                // 24字节文字数据
                // 0x10, 0x00, 0x10, 0x00, 0xFF, 0x01, 0x11, 0x01,
                // 0x11, 0x01, 0xFF, 0x01, 0x11, 0x01, 0x11, 0x01,
                // 0xFF, 0x01, 0x11, 0x04, 0x10, 0x04, 0xE0, 0x07,
            0x00, 0x00, 0xFE, 0x3F, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0xFF, 0x7F,
            0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x08, 0x04, 0x08, 0x04, 0x04, 0x04, 0x02, 0x04,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
            0x00, 0x00, 0xFE, 0x3F, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0xFF, 0x7F,
            0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x08, 0x04, 0x08, 0x04, 0x04, 0x04, 0x02, 0x04,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
            0x00, 0x00, 0xFE, 0x3F, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0xFF, 0x7F,
            0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x08, 0x04, 0x08, 0x04, 0x04, 0x04, 0x02, 0x04,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
            0x00, 0x00, 0xFE, 0x3F, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0xFF, 0x7F,
            0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x08, 0x04, 0x08, 0x04, 0x04, 0x04, 0x02, 0x04,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,

                0x0D, 0x0A                     // 包尾
            };
            
            for (int i = 0; i < sizeof(leftTextPacket); i++) {
                ParseResult result = btParser.parseByte(leftTextPacket[i], currentFrame);
                if (result != ParseResult::NEED_MORE_DATA) {
                    handleParseResult(result);
                }
            }
            
            Serial.println("📝 显示右上区域文字（1个字符测试）");
            
            // 发送右上文字区域命令
            uint8_t rightTextPacket[] = {
                0xAA, 0x55,                    // 包头
                0x21,                          // 计分模式命令
                0x00, 0x41,                    // 数据长度 
                0x02,                          // 右上区域 (0x02=右上文字区域)
                // 64字节文字数据
            0x00, 0x00, 0xFE, 0x3F, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0xFF, 0x7F,
            0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x10, 0x04, 0x08, 0x04, 0x08, 0x04, 0x04, 0x04, 0x02, 0x04,
            0x08, 0x04, 0x08, 0x04, 0x08, 0x04, 0x08, 0x02, 0x3F, 0x12, 0x24, 0x21, 0xA4, 0x7F, 0x24, 0x41,
            0x24, 0x00, 0x12, 0x3F, 0x14, 0x21, 0x08, 0x21, 0x14, 0x21, 0x22, 0x21, 0x01, 0x3F, 0x00, 0x21,
                0x0D, 0x0A                     // 包尾
            };
            
            for (int i = 0; i < sizeof(rightTextPacket); i++) {
                ParseResult result = btParser.parseByte(rightTextPacket[i], currentFrame);
                if (result != ParseResult::NEED_MORE_DATA) {
                    handleParseResult(result);
                }
            }
            
            textDisplayed = true;
            lastCommandTime = currentTime;
            Serial.println("✅ 文字区域显示完成，开始数字区域连续更新测试");

            // 🔍 测试函数状态验证
            printf("🔍 Test Function Status Verification:\n");
            printf("  - isScoreModeActive(): %s\n", isScoreModeActive() ? "TRUE" : "FALSE");

            return;
        }
        
       // 检查阶段超时
        if (currentTime - testStartTime > 5000) {
            Serial.println("🕐 切换到时钟大屏模式测试阶段 (5秒)");
            Serial.println("🔍 验证：时钟模式显示时无计分模式像素残留");
            
            // 发送时钟模式命令
            uint8_t clockPacket[] = {
                0xAA, 0x55,                    // 包头
                0x20,                          // 时钟模式命令
                0x00, 0x0C,                    // 数据长度 (12字节)
                0x01,                          // 大屏模式 (0x01=大屏)
                0x03,                          // GIF选择
                0xFF, 0x00, 0x00,             // 红色星期
                0x00, 0x00, 0xFF,             // 蓝色时间
                0x17, 0x3B,                   // 23:59
                0x03,                          // 周三
                0x00,                          // 中文
                0x0D, 0x0A                     // 包尾
            };
            
            for (int i = 0; i < sizeof(clockPacket); i++) {
                ParseResult result = btParser.parseByte(clockPacket[i], currentFrame);
                if (result != ParseResult::NEED_MORE_DATA) {
                    handleParseResult(result);
                }
            }
            
            testPhase = 1;
            testStartTime = currentTime;
            return;
        }
        
        // 发送数字区域命令 (每150ms一次，且文字已显示)
        if (textDisplayed && (currentTime - lastCommandTime > 150)) {
            if (commandCount % 2 == 0) {
                // 偶数次：测试左下区域 (region=0x01)，加法，测试边界999+1→0
                uint8_t leftScorePacket[] = {
                    0xAA, 0x55,                           // 包头
                    0x21,                                 // 计分模式命令
                    0x00, 0x09,                          // 数据长度 (9字节)
                    0x01,                                // 左下区域
                    0x00, 0xFF, 0x00,                    // 绿色
                    (uint8_t)(leftScore >> 8),           // 初值高位
                    (uint8_t)(leftScore & 0xFF),         // 初值低位
                    0x00,                                // 加法
                    0x00, 0x01,                          // 加1
                    0x0D, 0x0A                           // 包尾
                };
                
                for (int i = 0; i < sizeof(leftScorePacket); i++) {
                    ParseResult result = btParser.parseByte(leftScorePacket[i], currentFrame);
                    if (result != ParseResult::NEED_MORE_DATA) {
                        handleParseResult(result);
                    }
                }
                
                leftScore++;
                if (leftScore > 999) leftScore = 0;  // 测试溢出
                Serial.printf("🔢 左下数字区域测试 #%d: %d (验证：只更新左下数字，文字区域保持不变)\n", 
                             commandCount/2 + 1, leftScore);
                
            } else {
                // 奇数次：测试右下区域 (region=0x03)，减法，测试边界1-1→0
                uint8_t rightScorePacket[] = {
                    0xAA, 0x55,                           // 包头
                    0x21,                                 // 计分模式命令
                    0x00, 0x09,                          // 数据长度 (9字节)
                    0x03,                                // 右下区域
                    0xFF, 0x00, 0xFF,                    // 紫色
                    (uint8_t)(rightScore >> 8),          // 初值高位
                    (uint8_t)(rightScore & 0xFF),        // 初值低位
                    0x01,                                // 减法
                    0x00, 0x01,                          // 减1
                    0x0D, 0x0A                           // 包尾
                };
                
                for (int i = 0; i < sizeof(rightScorePacket); i++) {
                    ParseResult result = btParser.parseByte(rightScorePacket[i], currentFrame);
                    if (result != ParseResult::NEED_MORE_DATA) {
                        handleParseResult(result);
                    }
                }
                
                if (rightScore > 0) {
                    rightScore--;
                } else {
                    rightScore = 999;  // 测试下溢
                }
                Serial.printf("🔢 右下数字区域测试 #%d: %d (验证：只更新右下数字，文字区域保持不变)\n", 
                             commandCount/2 + 1, rightScore);
            }
            
            commandCount++;
            lastCommandTime = currentTime;
        }
    }
    
    // 时钟模式测试阶段 (5秒)
    else if (testPhase == 1) {
        // 检查阶段超时，循环回到计分模式
        if (currentTime - testStartTime > 5000) {
            Serial.println("🔄 返回计分模式测试 - 验证无时钟模式像素残留");
            Serial.println("🎯 关键验证：文字区域应该恢复显示，数字区域应该保持最后的值");
            testPhase = 0;
            testStartTime = currentTime;
            commandCount = 0;
            textDisplayed = false;  // 重新显示文字
            // 重置分数用于下一轮测试
            leftScore = 999;
            rightScore = 1;
        }
    }
}


// 🎮 GIF+文本混合模式特效测试函数
void testGifTextModeEffects() {
    // 测试状态枚举
    enum TestMode {
        MODE1_TEST = 0,  // 模式1: 左GIF + 右上下文本 (16x16)
        MODE2_TEST,      // 模式2: 左GIF + 右单文本 (32x32)
        MODE3_TEST,      // 模式3: 左上下文本 (16x16) + 右GIF
        MODE4_TEST,      // 模式4: 左单文本 (32x32) + 右GIF
        MODE_COUNT
    };
    
    enum EffectType {
        EFFECT_NORMAL = 0,    // 普通显示
        EFFECT_SCROLL,        // 滚动特效
        EFFECT_BLINK,         // 闪烁特效
        EFFECT_GROUP_SWITCH,  // 分组切换
        EFFECT_COUNT
    };
    
    // 静态变量保持测试状态
    static TestMode currentMode = MODE1_TEST;
    static EffectType currentEffect = EFFECT_NORMAL;
    static unsigned long lastModeChange = 0;
    static bool testInitialized = false;
    static bool effectApplied = false;
    // 在testGifTextModeEffects()中添加频率控制
static unsigned long lastEffectUpdate = 0;
static unsigned long lastGifUpdate = 0;
static unsigned long lastTextUpdate = 0;

unsigned long currentTime = millis();

// GIF更新：20fps (50ms间隔)
if (currentTime - lastGifUpdate >= 50) {
    updateGIF();
    lastGifUpdate = currentTime;
}

// 文本特效更新：10fps (100ms间隔)
if (currentTime - lastEffectUpdate >= 100) {
    updateAllRegionEffects();
    lastEffectUpdate = currentTime;
}

// 文本内容更新：5fps (200ms间隔)
if (currentTime - lastTextUpdate >= 200) {
    if (hasTextRegionNeedUpdate()) {
        updateTextRegionsDisplay();
    }
    lastTextUpdate = currentTime;
}
    // 测试数据定义
    static const uint16_t testData16x16[16] = {
        0x0000, 0x0000, 0x0006, 0x0004,
        0x0008, 0x0030, 0x00E0, 0x3F80,
        0x2F00, 0x00E0, 0x0030, 0x0008,
        0x0004, 0x0006, 0x0000, 0x0000,
    };
    
    static const uint16_t testData32x32[64] = {
        0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
        0x0000, 0x0000, 0x0000, 0x0C00, 0x0030, 0x0C00, 0x0030, 0x1C00,
        0x0070, 0x1800, 0x00E0, 0x1800, 0x01C0, 0x3800, 0x01D0, 0x3FFE,
        0x0398, 0x7FFE, 0x071C, 0x7018, 0x1E1C, 0x7018, 0x3E0E, 0xF018,
        0x3E07, 0xF018, 0x2603, 0xF018, 0x0603, 0xB018, 0x0607, 0x3018,
        0x0607, 0x3018, 0x060E, 0x3018, 0x061C, 0x3018, 0x0638, 0x3018,
        0x0670, 0x3018, 0x07E0, 0x3018, 0x07C0, 0x3018, 0x0780, 0x3FFE,
        0x0600, 0x3FFE, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
    };
    
    // 初始化测试
    if (!testInitialized) {
        Serial.println("\n🎮 ===== GIF+文本混合模式特效测试开始 =====");
        Serial.println("📋 测试内容：四种模式的区域文本特效功能");
        Serial.println("🎯 测试模式：");
        Serial.println("  模式1: 左GIF + 右上下文本 (16x16)");
        Serial.println("  模式2: 左GIF + 右单文本 (32x32)");
        Serial.println("  模式3: 左上下文本 (16x16) + 右GIF");
        Serial.println("  模式4: 左单文本 (32x32) + 右GIF");
        Serial.println("💫 特效测试：普通显示 → 滚动 → 闪烁 → 分组切换");
        Serial.println("");
        testInitialized = true;
        lastModeChange = millis();
    }
    
  
    if (currentTime - lastModeChange >= 5000) {
        // 清理当前特效
        if (effectApplied) {
            switch (currentMode) {
                case MODE1_TEST:
                    clearAllRegionEffects(MODE1_TEXT_UPPER);
                    clearAllRegionEffects(MODE1_TEXT_LOWER);
                    clearAllRegionEffects(MODE1_GIF_REGION);
                    break;
                case MODE3_TEST:
                    // 16x16模式：清理上下两个区域
                    clearAllRegionEffects(MODE3_TEXT_UPPER);
                    clearAllRegionEffects(MODE3_TEXT_LOWER);
                    clearAllRegionEffects(MODE3_GIF_REGION);
                    break;
                case MODE2_TEST:
                  clearAllRegionEffects(MODE2_TEXT_REGION);
                  clearAllRegionEffects(MODE2_GIF_REGION);
                  break;
                case MODE4_TEST:
                    // 32x32模式：清理全屏区域
                    clearAllRegionEffects(MODE4_TEXT_REGION);
                    clearAllRegionEffects(MODE4_GIF_REGION);
                    break;
            }
            effectApplied = false;
        }
        
        // 切换到下一个特效
        currentEffect = (EffectType)((currentEffect + 1) % EFFECT_COUNT);
        
        // 如果完成了所有特效，切换到下一个模式
        if (currentEffect == EFFECT_NORMAL) {
            currentMode = (TestMode)((currentMode + 1) % MODE_COUNT);
            Serial.printf("\n🚀 开始测试模式%d\n", currentMode + 1);
        }
        
        lastModeChange = currentTime;
    }
    
    // 应用当前模式和特效
    if (!effectApplied) {
        const char* modeNames[] = {
            "模式1(左GIF+右上下文本)",
            "模式2(左GIF+右单文本)",
            "模式3(左上下文本+右GIF)",
            "模式4(左单文本+右GIF)"
        };
        
        const char* effectNames[] = {
            "普通显示",
            "滚动特效",
            "闪烁特效",
            "分组切换"
        };
        
        Serial.printf("🎨 %s - %s\n", modeNames[currentMode], effectNames[currentEffect]);
        
        // 设置GIF显示位置
        switch (currentMode) {
            case MODE1_TEST:
                 // GIF在左侧
                setGifDisplayOffset(0, 0);
                changeStartupGIF(1);
                Serial.println("  🎬 GIF显示位置：左侧 (0,0)");
                break;

            case MODE2_TEST:
                // GIF在左侧
                setGifDisplayOffset(0, 0);
                Serial.println("  🎬 GIF显示位置：左侧 (0,0)");
                break;
            case MODE3_TEST:
                setGifDisplayOffset(32, 0);
                Serial.println("  🎬 GIF显示位置：右侧 (32,0)");
                break;

            case MODE4_TEST:
                // GIF在右侧
                setGifDisplayOffset(32, 0);
                Serial.println("  🎬 GIF显示位置：右侧 (32,0)");
                break;
        }
        
        // 根据模式设置文本区域
        switch (currentMode) {
            case MODE1_TEST: {
                // 模式1: 左GIF + 右上下文本 (16x16)
                Serial.println("  📝 设置右上文本区域 (TEXT_REGION_UPPER)");
                updateTextRegion(MODE1_TEXT_UPPER, testData16x16, 1, COLOR_RED);
                
                Serial.println("  📝 设置右下文本区域 (TEXT_REGION_LOWER)");
                updateTextRegion(MODE1_TEXT_LOWER, testData16x16, 1, COLOR_GREEN);
                
                // 应用特效
                switch (currentEffect) {
                    case EFFECT_NORMAL:
                        Serial.println("    💡 区域0: 普通显示");
                        Serial.println("    💡 区域1: 普通显示");
                        break;
                    case EFFECT_SCROLL:
                        setRegionScrollEffect(MODE1_TEXT_UPPER, true, 1, 5);
                        setRegionScrollEffect(MODE1_TEXT_LOWER, true, 2, 5);
                        Serial.println("    🌊 区域0: 启用滚动特效 (向左滚动，速度5)");
                        Serial.println("    🌊 区域1: 启用滚动特效 (向左滚动，速度5)");
                        break;
                    case EFFECT_BLINK:
                        setRegionBlinkEffect(MODE1_TEXT_UPPER, true, 7);
                        setRegionBlinkEffect(MODE1_TEXT_LOWER, true, 7);
                        Serial.println("    💫 区域0: 启用闪烁特效 (速度7)");
                        Serial.println("    💫 区域1: 启用闪烁特效 (速度7)");
                        break;
                    case EFFECT_GROUP_SWITCH: {
                        // 创建多字符数据用于分组切换
                        uint16_t multiCharData[64]; // 4个字符
                        for (int i = 0; i < 4; i++) {
                            memcpy(&multiCharData[i * 16], testData16x16, 16 * sizeof(uint16_t));
                        }
                        updateTextRegion(MODE1_TEXT_UPPER, multiCharData, 4, COLOR_RED);
                        updateTextRegion(MODE1_TEXT_LOWER, multiCharData, 4, COLOR_GREEN);
                        setRegionGroupSwitching(MODE1_TEXT_UPPER, true);
                        setRegionGroupSwitching(MODE1_TEXT_LOWER, true);
                        Serial.println("    🔄 区域0: 启用分组切换 (4字符)");
                        Serial.println("    🔄 区域1: 启用分组切换 (4字符)");
                        break;
                    }
                }
                break;
            }
            
            case MODE2_TEST: {
                // 模式2: 左GIF + 右单文本 (32x32)
                Serial.println("  📝 设置右侧文本区域 (TEXT_REGION_FULL)");
                updateTextRegion(MODE2_TEXT_REGION, testData32x32, 1, COLOR_BLUE);
                
                // 应用特效
                switch (currentEffect) {
                    case EFFECT_NORMAL:
                        Serial.println("    💡 区域: 普通显示");
                        break;
                    case EFFECT_SCROLL:
                        setRegionScrollEffect(MODE2_TEXT_REGION, true, 4, 5);
                        Serial.println("    🌊 区域: 启用滚动特效 (向左滚动，速度5)");
                        break;
                    case EFFECT_BLINK:
                        setRegionBlinkEffect(MODE2_TEXT_REGION, true, 7);
                        Serial.println("    💫 区域: 启用闪烁特效 (速度7)");
                        break;
                    case EFFECT_GROUP_SWITCH: {
                        // 创建多字符数据用于分组切换
                        uint16_t multiCharData[128]; // 2个32x32字符
                        for (int i = 0; i < 2; i++) {
                            memcpy(&multiCharData[i * 64], testData32x32, 64 * sizeof(uint16_t));
                        }
                        updateTextRegion(MODE2_TEXT_REGION, multiCharData, 2, COLOR_BLUE);
                        setRegionGroupSwitching(MODE2_TEXT_REGION, true);
                        Serial.println("    🔄 区域: 启用分组切换 (2字符)");
                        break;
                    }
                }
                break;
            }
            
            case MODE3_TEST: {
                // 模式3: 左上下文本 (16x16) + 右GIF
                Serial.println("  📝 设置左上文本区域 (TEXT_REGION_UPPER)");
                updateTextRegion(MODE3_TEXT_UPPER, testData16x16, 1, COLOR_YELLOW);
                
                Serial.println("  📝 设置左下文本区域 (TEXT_REGION_LOWER)");
                updateTextRegion(MODE3_TEXT_LOWER, testData16x16, 1, COLOR_CYAN);
                
                // 应用特效 (与模式1相同的逻辑)
                switch (currentEffect) {
                    case EFFECT_NORMAL:
                        Serial.println("    💡 区域0: 普通显示");
                        Serial.println("    💡 区域1: 普通显示");
                        break;
                    case EFFECT_SCROLL:
                        setRegionScrollEffect(MODE3_TEXT_UPPER, true, 2, 4);
                        setRegionScrollEffect(MODE3_TEXT_LOWER, true, 1, 4);
                        Serial.println("    🌊 区域0: 启用滚动特效 (向右滚动，速度4)");
                        Serial.println("    🌊 区域1: 启用滚动特效 (向右滚动，速度4)");
                        break;
                    case EFFECT_BLINK:
                        setRegionBlinkEffect(MODE3_TEXT_UPPER, true, 6);
                        setRegionBlinkEffect(MODE3_TEXT_LOWER, true, 6);
                        Serial.println("    💫 区域0: 启用闪烁特效 (速度6)");
                        Serial.println("    💫 区域1: 启用闪烁特效 (速度6)");
                        break;
                    case EFFECT_GROUP_SWITCH: {
                        uint16_t multiCharData[64];
                        for (int i = 0; i < 4; i++) {
                            memcpy(&multiCharData[i * 16], testData16x16, 16 * sizeof(uint16_t));
                        }
                        updateTextRegion(MODE3_TEXT_UPPER, multiCharData, 4, COLOR_YELLOW);
                        updateTextRegion(MODE3_TEXT_LOWER, multiCharData, 4, COLOR_CYAN);
                        setRegionGroupSwitching(MODE3_TEXT_UPPER, true);
                        setRegionGroupSwitching(MODE3_TEXT_LOWER, true);
                        Serial.println("    🔄 区域0: 启用分组切换 (4字符)");
                        Serial.println("    🔄 区域1: 启用分组切换 (4字符)");
                        break;
                    }
                }
                break;
            }
            
            case MODE4_TEST: {
                // 模式4: 左单文本 (32x32) + 右GIF
                Serial.println("  📝 设置左侧文本区域 (TEXT_REGION_FULL)");
                updateTextRegion(MODE4_TEXT_REGION, testData32x32, 1, COLOR_MAGENTA);
                
                // 应用特效 (与模式2相同的逻辑)
                switch (currentEffect) {
                    case EFFECT_NORMAL:
                        Serial.println("    💡 区域: 普通显示");
                        break;
                    case EFFECT_SCROLL:
                        setRegionScrollEffect(MODE4_TEXT_REGION, true, 3, 6);
                        Serial.println("    🌊 区域: 启用滚动特效 (向右滚动，速度6)");
                        break;
                    case EFFECT_BLINK:
                        setRegionBlinkEffect(MODE4_TEXT_REGION, true, 8);
                        Serial.println("    💫 区域: 启用闪烁特效 (速度8)");
                        break;
                    case EFFECT_GROUP_SWITCH: {
                        uint16_t multiCharData[128];
                        for (int i = 0; i < 2; i++) {
                            memcpy(&multiCharData[i * 64], testData32x32, 64 * sizeof(uint16_t));
                        }
                        updateTextRegion(MODE4_TEXT_REGION, multiCharData, 2, COLOR_MAGENTA);
                        setRegionGroupSwitching(MODE4_TEXT_REGION, true);
                        Serial.println("    🔄 区域: 启用分组切换 (2字符)");
                        break;
                    }
                }
                break;
            }
        }
        
        Serial.println("  ✅ 模式设置完成");
        effectApplied = true;
    }
    
    // 更新特效和显示 (这些函数本身就是无阻塞的)
    // updateAllRegionEffects();
    
    // // 检查是否有区域需要更新
    // if (hasTextRegionNeedUpdate()) {
    //     updateTextRegionsDisplay();
    // }
}
// 处理列表开始 (0x30)
void handleAppListStartCommand(const BluetoothFrame &frame)
{
    freeFileList(); // 清除之前的文件列表
    DEBUG_INFO("📂 start to receive list");
}

// 处理列表数据 (0x31)
void handleAppListDataCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < 1)
    {
        DEBUG_ERROR("❌ invalid list data");
        return;
    }

    String filename_forlist;
    filename_forlist=String((char *)frame.data,frame.dataLength);
    addFileToList(filename_forlist.c_str());
}

void handleAppListEndCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("✅ file list received");
    printFileList();
}
