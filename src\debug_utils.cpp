#include "debug_utils.h"

// 静态成员变量初始化
int DebugController::globalLevel = GLOBAL_DEBUG_LEVEL;
int DebugController::btLevel = BT_DEBUG_LEVEL;
int DebugController::fileLevel = FILE_DEBUG_LEVEL;
int DebugController::gifLevel = GIF_DEBUG_LEVEL;

void DebugController::setGlobalDebugLevel(int level) {
    if (level >= DEBUG_LEVEL_NONE && level <= DEBUG_LEVEL_VERBOSE) {
        globalLevel = level;
        Serial.printf("Global debug level set to %d\n", level);
    }
}

void DebugController::setBTDebugLevel(int level) {
    if (level >= DEBUG_LEVEL_NONE && level <= DEBUG_LEVEL_VERBOSE) {
        btLevel = level;
        Serial.printf("Bluetooth debug level set to %d\n", level);
    }
}

void DebugController::setFileDebugLevel(int level) {
    if (level >= DEBUG_LEVEL_NONE && level <= DEBUG_LEVEL_VERBOSE) {
        fileLevel = level;
        Serial.printf("File debug level set to %d\n", level);
    }
}

void DebugController::setGIFDebugLevel(int level) {
    if (level >= DEBUG_LEVEL_NONE && level <= DEBUG_LEVEL_VERBOSE) {
        gifLevel = level;
        Serial.printf("GIF debug level set to %d\n", level);
    }
}

int DebugController::getGlobalDebugLevel() {
    return globalLevel;
}

int DebugController::getBTDebugLevel() {
    return btLevel;
}

int DebugController::getFileDebugLevel() {
    return fileLevel;
}

int DebugController::getGIFDebugLevel() {
    return gifLevel;
}

void DebugController::printDebugStatus() {
    Serial.println("=== Debug Status ===");
    Serial.printf("Global Level: %d\n", globalLevel);
    Serial.printf("BT Level: %d\n", btLevel);
    Serial.printf("File Level: %d\n", fileLevel);
    Serial.printf("GIF Level: %d\n", gifLevel);
    Serial.println("Level meanings:");
    Serial.println("0=NONE, 1=ERROR, 2=WARN, 3=INFO, 4=DEBUG, 5=VERBOSE");
    Serial.println("===================");
}
