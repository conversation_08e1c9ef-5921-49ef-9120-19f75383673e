#ifndef GIF_PLAYER_H
#define GIF_PLAYER_H

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "AnimatedGIF.h"
#include "LittleFS.h"
#include "config.h"
#include "esp_heap_caps.h"

// GIF播放相关变量声明
extern MatrixPanel_I2S_DMA *dma_display;
extern AnimatedGIF gif;
extern File gifFile;
extern bool gifPlaying;
extern unsigned long lastGifFrame;
extern int gifFrameDelay;
extern int x_offset, y_offset;  // 添加偏移量变量

// LED矩阵屏和GIF相关函数声明
bool initLEDMatrix();
bool playGIF(const char* filename);
bool playGIFStream(const char* filename);  // 新增流式播放函数
bool playGIFAuto(const char* filename);    // 智能选择播放模式
void stopGIF();
void GIFDraw(GIFDRAW *pDraw);
void updateGIF();
void printMemoryInfo();  // 添加内存信息打印函数
bool canLoadGIF(const char* filename);  // 检查GIF文件是否可以加载
void forceGarbageCollection();  // 强制垃圾回收
bool changeStartupGIF(uint8_t number);  // 修改开机GIF
// 流式GIF播放回调函数
void* GIFOpenFile(const char *fname, int32_t *pSize);
void GIFCloseFile(void *pHandle);
int32_t GIFReadFile(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen);
int32_t GIFSeekFile(GIFFILE *pFile, int32_t iPosition);
void setGifDisplayOffset(int offset_x, int offset_y);     //设置gif初始坐标基准点

#endif // GIF_PLAYER_H
