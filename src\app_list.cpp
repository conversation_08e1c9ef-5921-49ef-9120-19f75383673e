#include "app_list.h"

// 存储文件名的动态数组
char **file_list = (char **)malloc(MAX_FILES * sizeof(char *));
int file_count = 0;

void initFileList() {
    // 初始化文件列表
    for (int i = 0; i < MAX_FILES; i++) {
        file_list[i] = nullptr;
    }
    file_count = 0;
}

void freeFileList() {
    // 释放文件列表内存
    for (int i = 0; i < file_count; i++) {
        if (file_list[i]) {
            free(file_list[i]);
            file_list[i] = nullptr; // 避免悬空指针
        }
    }
    file_count = 0;
}

void addFileToList(const char* filename) {
    if (file_count < MAX_FILES) {
        file_list[file_count] = (char *)malloc((strlen(filename) + 1) * sizeof(char));
        strcpy(file_list[file_count], filename);
        file_count++;
    } else {
        // 超出最大文件数量，处理错误
        DEBUG_ERROR("File list is full, cannot add more files");
    }
}

void printFileList() {
    // 打印文件列表
    for (int i = 0; i < file_count; i++) {
        if (file_list[i]) {
            Serial.println(file_list[i]);
        }
    }
}

