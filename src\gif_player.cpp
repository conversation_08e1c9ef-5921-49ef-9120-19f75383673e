#include "gif_player.h"
#include "debug_utils.h"

// GIF播放相关变量定义
MatrixPanel_I2S_DMA *dma_display = nullptr;
AnimatedGIF gif;
File gifFile;
bool gifPlaying = false;
unsigned long lastGifFrame = 0;
int gifFrameDelay = GIF_DEFAULT_FRAME_DELAY;
uint8_t* gifData = nullptr;  // 添加全局变量来跟踪分配的内存 // 默认帧延迟
int x_offset = 0, y_offset = 0;  // 显示偏移量
int gif_offset_x = GIF_DEFAULT_OFFSET_X;  // GIF显示X偏移量，默认为0
int gif_offset_y = GIF_DEFAULT_OFFSET_Y;  // GIF显示Y偏移量，默认为0
// 初始化LED矩阵屏
bool initLEDMatrix()
{
    HUB75_I2S_CFG mxconfig(
        SCREEN_WIDTH,       // 面板宽度
        SCREEN_HEIGHT,      // 面板高度
        PANEL_CHAIN_LENGTH  // 链式面板数量
    );

    // 配置引脚映射
    mxconfig.gpio.r1 = PIN_R1;
    mxconfig.gpio.g1 = PIN_G1;
    mxconfig.gpio.b1 = PIN_B1;
    mxconfig.gpio.r2 = PIN_R2;
    mxconfig.gpio.g2 = PIN_G2;
    mxconfig.gpio.b2 = PIN_B2;

    mxconfig.gpio.a = PIN_A;
    mxconfig.gpio.b = PIN_B;
    mxconfig.gpio.c = PIN_C;
    mxconfig.gpio.d = PIN_D;
    mxconfig.gpio.e = PIN_E;

    mxconfig.gpio.lat = PIN_LAT;
    mxconfig.gpio.oe = PIN_OE;
    mxconfig.gpio.clk = PIN_CLK;

    // 配置选项
    mxconfig.clkphase = false;
    mxconfig.driver = HUB75_I2S_CFG::MATRIX_DRIVER_CHIP;

    // 创建显示对象
    dma_display = new MatrixPanel_I2S_DMA(mxconfig);

    if (!dma_display->begin()) {
        Serial.println("LED matrix initialization failed");
        return false;
    }

    // 设置亮度
    dma_display->setBrightness8(MATRIX_BRIGHTNESS);
    dma_display->clearScreen();
    dma_display->fillScreen(dma_display->color565(0, 0, 0));

    Serial.println("LED matrix initialized successfully");
    Serial.printf("Screen size: %dx%d, Brightness: %d\n", SCREEN_WIDTH, SCREEN_HEIGHT, MATRIX_BRIGHTNESS);
    return true;
}

// 播放GIF动画（内存模式，适用于小文件）
bool playGIF(const char* filename)
{
    // 停止当前播放的GIF
    stopGIF();

    // 强制垃圾回收
    forceGarbageCollection();

    // 预检查文件是否可以加载
    if (!canLoadGIF(filename)) {
        GIF_INFO("Cannot load GIF file due to insufficient memory, trying stream mode: %s", filename);
        // 如果内存不足，尝试流式播放
        return playGIFStream(filename);
    }

    // 检查可用内存
    size_t freeHeap = ESP.getFreeHeap();
    GIF_DEBUG("Free heap before GIF load: %d bytes", freeHeap);

    // 打开GIF文件
    gifFile = LittleFS.open(filename, "r");
    if (!gifFile) {
        GIF_ERROR("Cannot open GIF file: %s", filename);
        return false;
    }

    // 检查文件大小
    size_t fileSize = gifFile.size();
    GIF_DEBUG("GIF file size: %d bytes", fileSize);

    // 检查是否有足够内存（需要额外的缓冲区空间）
    size_t requiredMemory = fileSize + 10000;  // 文件大小 + 10KB缓冲区
    if (requiredMemory > freeHeap) {
        GIF_WARN("Insufficient memory for GIF file. Need: %d, Available: %d", requiredMemory, freeHeap);
        gifFile.close();
        // 尝试流式播放
        return playGIFStream(filename);
    }
    GIF_DEBUG("Memory check passed. Required: %d, Available: %d", requiredMemory, freeHeap);

    // 尝试分配内存，对于没有PSRAM的设备直接使用堆内存
    if (ESP.getPsramSize() > 0) {
        // 有PSRAM的情况，优先使用PSRAM
        gifData = (uint8_t*)ps_malloc(fileSize);
        if (gifData) {
            GIF_DEBUG("Using PSRAM for GIF data");
        }
    }

    if (!gifData) {
        // 使用普通堆内存
        gifData = (uint8_t*)malloc(fileSize);
        if (!gifData) {
            GIF_ERROR("Memory allocation failed for %d bytes", fileSize);
            gifFile.close();
            // 尝试流式播放
            return playGIFStream(filename);
        }
        Serial.println("Using heap memory for GIF data");
    }

    // 读取文件到内存
    size_t bytesRead = gifFile.read(gifData, fileSize);
    gifFile.close();

    if (bytesRead != fileSize) {
        Serial.printf("File read error: expected %d bytes, got %d bytes\n", fileSize, bytesRead);
        free(gifData);
        gifData = nullptr;
        return false;
    }

    // 初始化GIF解码器
    if (gif.open(gifData, fileSize, GIFDraw)) {
        Serial.printf("Starting GIF playback (memory mode): %s\n", filename);
        Serial.printf("GIF size: %dx%d\n", gif.getCanvasWidth(), gif.getCanvasHeight());
        Serial.printf("Free heap after GIF load: %d bytes\n", ESP.getFreeHeap());

        // 计算居中偏移量
        x_offset = (SCREEN_WIDTH - gif.getCanvasWidth()) / 2;
        y_offset = (SCREEN_HEIGHT - gif.getCanvasHeight()) / 2;
        if (x_offset < 0) x_offset = 0;
        if (y_offset < 0) y_offset = 0;

        gifPlaying = true;
        lastGifFrame = millis();
        return true;
    } else {
        Serial.printf("GIF decoder initialization failed: %s\n", filename);
        free(gifData);
        gifData = nullptr;
        return false;
    }
}

bool changeStartupGIF(uint8_t number)
{
    // 构建完整路径
    char filename[20];
    snprintf(filename, sizeof(filename), "%02d.gif", number);

    String fullPath = String(GIF_STORAGE_PATH) + filename;

    printf("Attempting to change startup GIF to: %s (auto mode)\n", fullPath.c_str());

    // 检查文件是否存在
    if (!LittleFS.exists(fullPath)) {
        printf("Startup GIF not found: %s\n", fullPath.c_str());
        return false;
    }

    // 检查当前内存状态
    printf("Free heap before change: %d bytes\n", ESP.getFreeHeap());

    // 停止当前播放的GIF
    stopGIF();
    delay(100);  // 确保清理完成

    printf("Free heap after cleanup: %d bytes\n", ESP.getFreeHeap());

    // 使用智能选择播放模式（会自动选择流式播放）
    if (playGIFAuto(fullPath.c_str())) {
        printf("Startup animation successfully changed to: %s (auto mode)\n", filename);
        return true;
    } else {
        printf("Failed to play startup GIF: %s\n", filename);
        return false;
    }
}

// 流式播放GIF动画（适用于大文件）
bool playGIFStream(const char* filename)
{
    // 停止当前播放的GIF
    stopGIF();

    // 强制垃圾回收
    forceGarbageCollection();

    Serial.printf("Starting GIF stream playback: %s\n", filename);

    // 使用流式接口打开GIF
    if (gif.open(filename, GIFOpenFile, GIFCloseFile, GIFReadFile, GIFSeekFile, GIFDraw)) {
        Serial.printf("Successfully opened GIF stream; Canvas size = %d x %d\n",
                     gif.getCanvasWidth(), gif.getCanvasHeight());

        // 计算居中偏移量
        x_offset = (SCREEN_WIDTH - gif.getCanvasWidth()) / 2;
        y_offset = (SCREEN_HEIGHT - gif.getCanvasHeight()) / 2;
        if (x_offset < 0) x_offset = 0;
        if (y_offset < 0) y_offset = 0;

        Serial.printf("Display offset: x=%d, y=%d\n", x_offset, y_offset);
        Serial.printf("Free heap after GIF stream open: %d bytes\n", ESP.getFreeHeap());

        gifPlaying = true;
        lastGifFrame = millis();
        return true;
    } else {
        Serial.printf("Failed to open GIF stream: %s\n", filename);
        return false;
    }
}

// 智能选择播放模式（推荐使用此函数）
bool playGIFAuto(const char* filename)
{
    if (!LittleFS.exists(filename)) {
        Serial.printf("GIF file not found: %s\n", filename);
        return false;
    }

    File file = LittleFS.open(filename, "r");
    if (!file) {
        Serial.printf("Cannot open GIF file: %s\n", filename);
        return false;
    }

    size_t fileSize = file.size();
    file.close();

    Serial.printf("Auto-selecting playback mode for %s (size: %d bytes)\n", filename, fileSize);

    // 根据文件大小和可用内存智能选择播放模式
    // if (fileSize <= GIF_MEMORY_THRESHOLD && canLoadGIF(filename)) {
    //     Serial.println("Using memory mode for optimal performance");
    //     return playGIF(filename);
    // } else {
        Serial.println("Using stream mode for large file support");
        return playGIFStream(filename);
    // }
}

// 停止GIF播放
void stopGIF()
{
    if (gifPlaying) {
        gif.close();
        if (gifFile) {
            gifFile.close();
        }
        gifPlaying = false;
        Serial.println("GIF playback stopped");
    }

    // 释放分配的内存
    if (gifData) {
        free(gifData);
        gifData = nullptr;
        Serial.println("GIF memory freed");
    }
}

// GIF绘制回调函数
void GIFDraw(GIFDRAW *pDraw)
{
    uint8_t *s;
    uint16_t *d, *usPalette, usTemp[320];
    int x, y, iWidth;
    int actual_x, actual_y;  // 应用偏移后的实际坐标

    iWidth = pDraw->iWidth;
    if (iWidth > SCREEN_WIDTH)
        iWidth = SCREEN_WIDTH;

    usPalette = pDraw->pPalette;
    y = pDraw->iY + pDraw->y; // 当前行

    // 应用Y偏移量
    actual_y = y + gif_offset_y;

    // Y坐标边界检查
    if (actual_y >= SCREEN_HEIGHT || actual_y < 0 || pDraw->y >= pDraw->iHeight)
        return;

    s = pDraw->pPixels;

    if (pDraw->ucDisposalMethod == 2) {
        // 恢复到背景色
        for (x = 0; x < iWidth; x++) {
            if (s[x] == pDraw->ucTransparent)
                s[x] = pDraw->ucBackground;
        }
        pDraw->ucHasTransparency = 0;
    }

    // 应用调色板并绘制到屏幕
    if (pDraw->ucHasTransparency) {
        uint8_t ucTransparent = pDraw->ucTransparent;
        uint8_t c;
        int x, iCount;
        for (x = 0; x < iWidth; x++) {
            c = s[x];
            if (c != ucTransparent) {
                // 应用X偏移量并进行边界检查
                actual_x = pDraw->iX + x + gif_offset_x;
                if (actual_x >= 0 && actual_x < SCREEN_WIDTH) {
                    uint16_t color = usPalette[c];
                    dma_display->drawPixel(actual_x, actual_y, color);
                }
            }
        }
    } else {
        s = pDraw->pPixels;
        for (x = 0; x < iWidth; x++) {
            // 应用X偏移量并进行边界检查
            actual_x = pDraw->iX + x + gif_offset_x;
            if (actual_x >= 0 && actual_x < SCREEN_WIDTH) {
                uint16_t color = usPalette[s[x]];
                 dma_display->drawPixel(actual_x, actual_y, color);
            }
        }
    }
}


// 更新GIF动画
void updateGIF()
{
    if (!gifPlaying) {
        return;
    }

    unsigned long currentTime = millis();
    if (currentTime - lastGifFrame >= gifFrameDelay) {
        int result = gif.playFrame(true, NULL);
        if (result == 0) {
            // GIF播放完成，重新开始
            gif.reset();
        } else if (result < 0) {
            // 播放出错，停止播放 - 只在错误级别输出，避免频繁打印
            GIF_ERROR("GIF playback error, stopping");
            stopGIF();
            return;
        }
        lastGifFrame = currentTime;
    }
}

// 打印内存信息
void printMemoryInfo()
{
    Serial.println("=== Memory Information ===");
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("Largest free block: %d bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("Total heap size: %d bytes\n", ESP.getHeapSize());
    Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
    Serial.printf("Total PSRAM: %d bytes\n", ESP.getPsramSize());
    Serial.println("===========================");
}

// 检查GIF文件是否可以加载到内存
bool canLoadGIF(const char* filename)
{
    File file = LittleFS.open(filename, "r");
    if (!file) {
        Serial.printf("Cannot open file: %s\n", filename);
        return false;
    }

    size_t fileSize = file.size();
    file.close();

    // 如果文件大小超过阈值，建议使用流式播放
    if (fileSize > GIF_MEMORY_THRESHOLD) {
        Serial.printf("File size %d bytes exceeds memory threshold %d bytes, recommend stream mode\n",
                     fileSize, GIF_MEMORY_THRESHOLD);
        return false;
    }

    size_t freeHeap = ESP.getFreeHeap();
    size_t freePsram = ESP.getFreePsram();
    size_t totalFree = freeHeap + freePsram;

    Serial.printf("File size: %d bytes, Available memory: %d bytes (Heap: %d, PSRAM: %d)\n",
                 fileSize, totalFree, freeHeap, freePsram);

    // 需要文件大小 + 20KB缓冲区空间（增加缓冲区以提高稳定性）
    size_t requiredMemory = fileSize + 20480;
    bool canLoad = (requiredMemory <= totalFree);

    if (!canLoad) {
        Serial.printf("Insufficient memory for file. Need: %d, Available: %d\n", requiredMemory, totalFree);
    }

    return canLoad;
}

// 强制垃圾回收和内存整理
void forceGarbageCollection()
{
    Serial.println("Starting memory cleanup...");

    // 多次触发垃圾回收以确保内存碎片整理
    for (int i = 0; i < 3; i++) {
        ESP.getMaxAllocHeap();
        delay(20);
    }

    // 如果有PSRAM，也尝试整理PSRAM
    if (ESP.getPsramSize() > 0) {
        heap_caps_malloc_extmem_enable(1024); // 启用外部内存分配
    }

    delay(100);  // 给系统更多时间进行内存整理

    Serial.printf("After garbage collection - Free heap: %d bytes, Free PSRAM: %d bytes\n",
                 ESP.getFreeHeap(), ESP.getFreePsram());
    Serial.printf("Largest free block: %d bytes\n", ESP.getMaxAllocHeap());
}

// ========== 流式GIF播放回调函数 ==========

// 打开文件回调
void* GIFOpenFile(const char *fname, int32_t *pSize)
{
    Serial.printf("Opening GIF file: %s\n", fname);
    gifFile = LittleFS.open(fname, "r");
    if (gifFile) {
        *pSize = gifFile.size();
        Serial.printf("File size: %d bytes\n", *pSize);
        return (void *)&gifFile;
    }
    Serial.printf("Failed to open file: %s\n", fname);
    return NULL;
}

// 关闭文件回调
void GIFCloseFile(void *pHandle)
{
    File *f = static_cast<File *>(pHandle);
    if (f != NULL && *f) {
        f->close();
        Serial.println("GIF file closed");
    }
}

// 读取文件回调
int32_t GIFReadFile(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen)
{
    int32_t iBytesRead;
    File *f = static_cast<File *>(pFile->fHandle);

    if (!f || !*f) {
        return 0;
    }

    iBytesRead = iLen;
    // 注意：如果读取到文件的最后一个字节，seek()可能会停止工作
    if ((pFile->iSize - pFile->iPos) < iLen) {
        iBytesRead = pFile->iSize - pFile->iPos - 1; // 避免读取到最后一个字节
    }

    if (iBytesRead <= 0) {
        return 0;
    }

    iBytesRead = (int32_t)f->read(pBuf, iBytesRead);
    pFile->iPos = f->position();

    return iBytesRead;
}

// 文件定位回调
int32_t GIFSeekFile(GIFFILE *pFile, int32_t iPosition)
{
    File *f = static_cast<File *>(pFile->fHandle);

    if (!f || !*f) {
        return 0;
    }

    if (f->seek(iPosition)) {
        pFile->iPos = iPosition;
        return iPosition;
    }

    return 0;
}

// 设置GIF显示偏移量
void setGifDisplayOffset(int offset_x, int offset_y)
{
    // 边界检查，确保偏移量在合理范围内
    gif_offset_x = constrain(offset_x, -SCREEN_WIDTH, SCREEN_WIDTH);
    gif_offset_y = constrain(offset_y, -SCREEN_HEIGHT, SCREEN_HEIGHT);

    Serial.printf("GIF display offset set to: (%d, %d)\n", gif_offset_x, gif_offset_y);
}