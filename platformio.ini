; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
board_build.partitions = partitions_custom.csv
board_build.filesystem = littlefs
upload_protocol = esptool
monitor_speed = 115200

build_flags =
    -DCONFIG_BT_SPP_MAX_TX_BUFFER_SIZE=8192
    -DCONFIG_BT_SPP_MAX_RX_BUFFER_SIZE=8192
    -DCONFIG_BT_SPP_TX_BUFFER_SIZE=8192
    -DCONFIG_BT_SPP_RX_BUFFER_SIZE=8192
    -DCORE_DEBUG_LEVEL=0
    -DCONFIG_BT_ALLOCATION_FROM_SPIRAM_FIRST=0
    -DCONFIG_BT_BLE_DYNAMIC_ENV_MEMORY=1
    -DCON<PERSON>G_BT_SPP_CONGESTION_CONTROL=1
lib_deps =
    mrfaptastic/ESP32 HUB75 LED MATRIX PANEL DMA Display@^3.0.12
    adafruit/Adafruit GFX Library@^1.11.9
    adafruit/Adafruit BusIO@^1.14.5
    bitbank2/AnimatedGIF@^2.0.0

